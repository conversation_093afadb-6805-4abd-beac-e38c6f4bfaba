# Smart Lead Extractor Automation

This Python script automates the Smart Lead Extractor Chrome extension to extract data for multiple keywords and locations automatically.

## Prerequisites

1. **Chrome Browser** with the Smart Lead Extractor extension installed
2. **Python 3.7+** installed on your system
3. The extension should be accessible at: `chrome-extension://dlfcpdjocjjmobbdljgjlmiapgipaobf/views/index.html`

## Setup Instructions

### 1. Install Python Dependencies

```bash
pip install -r requirements.txt
```

### 2. Test Your Setup (Recommended)

Before running the main automation, test your setup:

```bash
python test_setup.py
```

This will verify:
- Python version compatibility
- Required dependencies
- File existence and content
- Chrome driver functionality

### 3. Verify File Structure

Make sure you have the following files in your directory:
- `smart_lead_extractor_automation.py` (main script)
- `keywords.txt` (list of keywords, one per line)
- `location.txt` (list of pin codes, one per line)
- `requirements.txt` (Python dependencies)

### 4. Prepare Your Data Files

**keywords.txt**: Contains the search keywords (already provided)
```
City Localities / Neighborhoods 
Popular Tourist Attractions 
Hidden Gems / Offbeat Places 
...
```

**location.txt**: Contains the pin codes (already provided)
```
382210
382430
382433
...
```

## Usage

### Run the Automation Script

```bash
python smart_lead_extractor_automation.py
```

## How It Works

The script follows this workflow:

1. **Setup**: Configures Chrome browser with the extension
2. **Load Extension**: Opens the Smart Lead Extractor extension
3. **Main Loop**: For each keyword:
   - For each location (pin code):
     - Enter keyword and location in the form
     - Start the extraction process
     - Wait for extraction to complete
     - Download the results
     - Clear data for next iteration
4. **File Management**: Downloads are saved to `extracted_data/` folder

## Features

- **Automated Processing**: Processes all keyword-location combinations automatically
- **Progress Tracking**: Logs progress and status updates
- **Error Handling**: Continues processing even if individual extractions fail
- **File Organization**: Downloads are saved in organized folders
- **Timeout Management**: Configurable timeouts for extraction and download processes

## Configuration

You can modify these settings in the script:

```python
EXTRACTION_TIMEOUT = 600  # 10 minutes per extraction
DOWNLOAD_TIMEOUT = 300   # 5 minutes per download
RESULTS_FOLDER = "extracted_data"  # Download folder
```

## Logs

The script creates detailed logs in:
- `automation.log` (file)
- Console output (real-time)

## Troubleshooting

### Common Issues

1. **Extension Not Loading**
   - Verify the extension is installed and enabled
   - Check if the extension URL is correct
   - Try opening the extension manually first

2. **Elements Not Found**
   - The extension interface may have changed
   - Check if the extension is fully loaded before starting

3. **Download Issues**
   - Ensure Chrome has permission to download files
   - Check if the download folder is accessible

4. **Timeout Errors**
   - Increase timeout values for slow extractions
   - Check internet connection stability

### Manual Verification

Before running the automation:
1. Open Chrome and navigate to the extension URL manually
2. Verify you can enter keywords and locations
3. Test the start/stop and download functionality

## File Structure After Running

```
smart_lead_extractor/
├── smart_lead_extractor_automation.py
├── requirements.txt
├── keywords.txt
├── location.txt
├── README.md
├── automation.log
└── extracted_data/
    ├── downloaded_file_1.csv
    ├── downloaded_file_2.csv
    └── ...
```

## Support

If you encounter issues:
1. Check the `automation.log` file for detailed error messages
2. Verify all prerequisites are met
3. Test the extension manually before running automation
4. Ensure stable internet connection

## Notes

- The script processes **22 keywords × 10 locations = 220 total combinations**
- Each extraction may take several minutes depending on data availability
- Total runtime can be several hours for all combinations
- The script can be stopped and resumed (though it will restart from the beginning)
