#!/usr/bin/env python3
"""
Smart Lead Extractor Automation Script - Version 2

This version uses a different approach to work around Chrome's extension blocking.
Instead of directly accessing the extension URL, it opens Chrome and guides the user
through a semi-automated process.

Requirements:
- Chrome browser with Smart Lead Extractor extension installed
- Python packages: selenium, webdriver-manager
- keywords.txt and location.txt files in the same directory
"""

import os
import time
import logging
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.common.keys import Keys

# Import configuration
try:
    from config import *
except ImportError:
    # Fallback configuration if config.py is not found
    EXTENSION_URL = "chrome-extension://dlfcpdjocjj<PERSON><PERSON>dljgjlmiapgipaobf/views/index.html"
    KEYWORDS_FILE = "keywords.txt"
    LOCATIONS_FILE = "location.txt"
    DOWNLOAD_TIMEOUT = 300
    EXTRACTION_TIMEOUT = 600
    RESULTS_FOLDER = "extracted_data"
    PAGE_LOAD_TIMEOUT = 30
    DELAY_BETWEEN_ITERATIONS = 5
    DELAY_AFTER_DOWNLOAD = 15
    DELAY_AFTER_INPUT = 2
    CHROME_HEADLESS = False
    CHROME_WINDOW_SIZE = "1920,1080"
    LOG_LEVEL = "INFO"
    LOG_FILE = "automation.log"
    MAX_RETRIES = 3
    RETRY_DELAY = 10

# Setup logging
logging.basicConfig(
    level=getattr(logging, LOG_LEVEL, logging.INFO),
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class SmartLeadExtractorAutomationV2:
    def __init__(self):
        self.driver = None
        self.wait = None
        self.keywords = []
        self.locations = []
        
    def setup_chrome_driver(self):
        """Setup Chrome driver with necessary options"""
        logger.info("Setting up Chrome driver...")
        
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument(f"--window-size={CHROME_WINDOW_SIZE}")
        # Remove automation detection flags for better compatibility
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        chrome_options.add_experimental_option("detach", True)
        
        # Set download directory
        download_dir = os.path.join(os.getcwd(), RESULTS_FOLDER)
        os.makedirs(download_dir, exist_ok=True)
        
        prefs = {
            "download.default_directory": download_dir,
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        # Use the specific ChromeDriver path
        chromedriver_path = r"C:\Users\<USER>\Desktop\web driver\chromedriver.exe"
        
        try:
            # Check if the ChromeDriver file exists
            if os.path.exists(chromedriver_path):
                logger.info(f"Using ChromeDriver from: {chromedriver_path}")
                service = Service(chromedriver_path)
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
            else:
                logger.warning(f"ChromeDriver not found at {chromedriver_path}")
                # Fallback: Use webdriver-manager
                from webdriver_manager.chrome import ChromeDriverManager
                logger.info("Attempting to setup ChromeDriver using webdriver-manager...")
                service = Service(ChromeDriverManager().install())
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
        except Exception as e:
            logger.warning(f"Primary ChromeDriver setup failed: {e}")
            try:
                # Final fallback: Use system ChromeDriver
                logger.info("Attempting to use system ChromeDriver...")
                self.driver = webdriver.Chrome(options=chrome_options)
            except Exception as e2:
                logger.error(f"All ChromeDriver setup methods failed: {e2}")
                raise Exception(f"Failed to initialize ChromeDriver: {e2}")
        
        self.wait = WebDriverWait(self.driver, PAGE_LOAD_TIMEOUT)
        logger.info("Chrome driver setup complete")
        
    def read_file_lines(self, filename):
        """Read lines from a text file and return as list"""
        try:
            with open(filename, 'r', encoding='utf-8') as file:
                lines = [line.strip() for line in file.readlines() if line.strip()]
            logger.info(f"Read {len(lines)} lines from {filename}")
            return lines
        except FileNotFoundError:
            logger.error(f"File {filename} not found!")
            return []
        except Exception as e:
            logger.error(f"Error reading {filename}: {str(e)}")
            return []
    
    def open_extension_manually(self):
        """Open Chrome and guide user to open the extension manually"""
        logger.info("Opening Chrome browser...")
        
        # Open a new tab and navigate to Chrome extensions page
        self.driver.get("chrome://extensions/")
        time.sleep(3)
        
        print("\n" + "="*60)
        print("MANUAL SETUP REQUIRED")
        print("="*60)
        print("Chrome has opened to the Extensions page.")
        print("Please follow these steps:")
        print()
        print("1. Find 'Smart Lead Extractor' in the extensions list")
        print("2. Click on the extension to open it")
        print("3. OR manually navigate to:")
        print(f"   {EXTENSION_URL}")
        print()
        print("4. Once the extension is open, press ENTER here to continue...")
        print("="*60)
        
        input("Press ENTER when the Smart Lead Extractor extension is open and ready...")
        
        # Try to switch to the extension tab
        logger.info("Looking for extension tab...")
        extension_found = False
        
        # Check all open tabs for the extension
        for handle in self.driver.window_handles:
            self.driver.switch_to.window(handle)
            current_url = self.driver.current_url
            if "chrome-extension://" in current_url and "dlfcpdjocjjmobbdljgjlmiapgipaobf" in current_url:
                logger.info(f"Found extension tab: {current_url}")
                extension_found = True
                break
        
        if not extension_found:
            logger.warning("Extension tab not found automatically")
            print("\nCould not automatically find the extension tab.")
            print("Please make sure the Smart Lead Extractor extension is open")
            print("and press ENTER to continue...")
            input()
        
        return True
    
    def wait_for_user_confirmation(self, message, timeout=30):
        """Wait for user to confirm an action"""
        print(f"\n{message}")
        print("Press ENTER when ready to continue...")
        input()
        return True
    
    def run_semi_automated_process(self):
        """Run the semi-automated extraction process"""
        logger.info("Starting semi-automated Smart Lead Extractor process...")
        
        # Read keywords and locations
        self.keywords = self.read_file_lines(KEYWORDS_FILE)
        self.locations = self.read_file_lines(LOCATIONS_FILE)
        
        if not self.keywords or not self.locations:
            logger.error("No keywords or locations found. Please check your files.")
            return
        
        logger.info(f"Found {len(self.keywords)} keywords and {len(self.locations)} locations")
        
        try:
            # Setup Chrome driver
            self.setup_chrome_driver()
            
            # Open extension manually
            if not self.open_extension_manually():
                return
            
            # Main automation loop
            total_combinations = len(self.keywords) * len(self.locations)
            current_combination = 0
            
            print(f"\n{'='*60}")
            print(f"STARTING EXTRACTION PROCESS")
            print(f"Total combinations to process: {total_combinations}")
            print(f"Keywords: {len(self.keywords)}")
            print(f"Locations: {len(self.locations)}")
            print(f"{'='*60}")
            
            for keyword_index, keyword in enumerate(self.keywords, 1):
                logger.info(f"Processing keyword {keyword_index}/{len(self.keywords)}: '{keyword}'")
                
                print(f"\n{'='*40}")
                print(f"KEYWORD {keyword_index}/{len(self.keywords)}: {keyword}")
                print(f"{'='*40}")
                
                for location_index, location in enumerate(self.locations, 1):
                    current_combination += 1
                    logger.info(f"Processing combination {current_combination}/{total_combinations}")
                    
                    print(f"\nCombination {current_combination}/{total_combinations}")
                    print(f"Keyword: '{keyword}'")
                    print(f"Location: '{location}'")
                    print("-" * 40)
                    
                    # Guide user through the process
                    print("Please follow these steps in the Smart Lead Extractor:")
                    print(f"1. Enter keyword: '{keyword}'")
                    print(f"2. Enter location: '{location}'")
                    print("3. Click 'Start Extracting'")
                    print("4. Wait for extraction to complete")
                    print("5. Click 'Download' when results are ready")
                    print("6. Clear data if not the last combination")
                    
                    self.wait_for_user_confirmation("Complete the above steps, then press ENTER to continue...")
                    
                    # Small delay between iterations
                    time.sleep(DELAY_BETWEEN_ITERATIONS)
                
                logger.info(f"Completed all locations for keyword: '{keyword}'")
            
            print(f"\n{'='*60}")
            print("EXTRACTION PROCESS COMPLETED!")
            print(f"Processed {total_combinations} combinations successfully")
            print(f"Check the '{RESULTS_FOLDER}' folder for downloaded files")
            print(f"{'='*60}")
            
            logger.info("Semi-automated process completed successfully!")
            
        except Exception as e:
            logger.error(f"Process failed: {str(e)}")
        
        finally:
            print("\nKeeping browser open for manual verification...")
            print("Close this script when you're done.")
            print("The browser will remain open for your use.")
            
            # Keep the script running so browser stays open
            try:
                while True:
                    time.sleep(10)
            except KeyboardInterrupt:
                logger.info("Script terminated by user")
                if self.driver:
                    logger.info("Closing browser...")
                    self.driver.quit()


def main():
    """Main function"""
    print("Smart Lead Extractor Semi-Automated Process")
    print("=" * 50)
    print(f"Keywords file: {KEYWORDS_FILE}")
    print(f"Locations file: {LOCATIONS_FILE}")
    print(f"Results folder: {RESULTS_FOLDER}")
    print("=" * 50)
    print()
    print("NOTE: This is a semi-automated process.")
    print("You will need to manually interact with the extension")
    print("while the script guides you through each step.")
    print("=" * 50)
    
    automation = SmartLeadExtractorAutomationV2()
    automation.run_semi_automated_process()


if __name__ == "__main__":
    main()
