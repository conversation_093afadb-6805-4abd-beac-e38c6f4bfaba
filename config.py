# Configuration file for Smart Lead Extractor Automation

# Extension URL
EXTENSION_URL = "chrome-extension://dlfcpdjocjjmobbdljgjlmiapgipaobf/views/index.html"

# File paths
KEYWORDS_FILE = "keywords.txt"
LOCATIONS_FILE = "location.txt"
RESULTS_FOLDER = "extracted_data"

# Timeouts (in seconds)
EXTRACTION_TIMEOUT = 600  # 10 minutes per extraction
DOWNLOAD_TIMEOUT = 300    # 5 minutes per download
PAGE_LOAD_TIMEOUT = 30    # 30 seconds for page loads

# Delays (in seconds)
DELAY_BETWEEN_ITERATIONS = 5    # Delay between each keyword-location combination
DELAY_AFTER_DOWNLOAD = 15       # Wait time after clicking download
DELAY_AFTER_INPUT = 2           # Wait time after entering text

# Chrome options
CHROME_HEADLESS = False         # Set to True to run Chrome in headless mode
CHROME_WINDOW_SIZE = "1920,1080"  # Browser window size

# Logging
LOG_LEVEL = "INFO"              # DEBUG, INFO, WARNING, ERROR
LOG_FILE = "automation.log"

# Retry settings
MAX_RETRIES = 3                 # Maximum retries for failed operations
RETRY_DELAY = 10                # Delay between retries (seconds)
