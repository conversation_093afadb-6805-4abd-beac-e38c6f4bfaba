#!/usr/bin/env python3
"""
Smart Lead Extractor Automation Script

This script automates the Smart Lead Extractor Chrome extension to extract data
for multiple keywords and locations automatically.

Requirements:
- Chrome browser with Smart Lead Extractor extension installed
- Python packages: selenium, webdriver-manager
- keywords.txt and location.txt files in the same directory

Usage:
    python smart_lead_extractor_automation.py
"""

import os
import time
import logging
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager

# Import configuration
try:
    from config import *
except ImportError:
    # Fallback configuration if config.py is not found
    EXTENSION_URL = "chrome-extension://dlfcpdjocjj<PERSON>bbdljgjlmiapgipaobf/views/index.html"
    KEYWORDS_FILE = "keywords.txt"
    LOCATIONS_FILE = "location.txt"
    DOWNLOAD_TIMEOUT = 300
    EXTRACTION_TIMEOUT = 600
    RESULTS_FOLDER = "extracted_data"
    PAGE_LOAD_TIMEOUT = 30
    DELAY_BETWEEN_ITERATIONS = 5
    DELAY_AFTER_DOWNLOAD = 15
    DELAY_AFTER_INPUT = 2
    CHROME_HEADLESS = False
    CHROME_WINDOW_SIZE = "1920,1080"
    LOG_LEVEL = "INFO"
    LOG_FILE = "automation.log"
    MAX_RETRIES = 3
    RETRY_DELAY = 10

# Setup logging
logging.basicConfig(
    level=getattr(logging, LOG_LEVEL, logging.INFO),
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class SmartLeadExtractorAutomation:
    def __init__(self):
        self.driver = None
        self.wait = None
        self.keywords = []
        self.locations = []
        
    def setup_chrome_driver(self):
        """Setup Chrome driver with necessary options"""
        logger.info("Setting up Chrome driver...")

        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_argument(f"--window-size={CHROME_WINDOW_SIZE}")
        chrome_options.add_argument("--disable-web-security")
        chrome_options.add_argument("--allow-running-insecure-content")
        chrome_options.add_argument("--disable-extensions-except")
        chrome_options.add_argument("--load-extension")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        # Add headless mode if configured
        if CHROME_HEADLESS:
            chrome_options.add_argument("--headless")
            logger.info("Running in headless mode")

        # Set download directory
        download_dir = os.path.join(os.getcwd(), RESULTS_FOLDER)
        os.makedirs(download_dir, exist_ok=True)

        prefs = {
            "download.default_directory": download_dir,
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True
        }
        chrome_options.add_experimental_option("prefs", prefs)

        # Use the specific ChromeDriver path provided by user
        chromedriver_path = r"C:\Users\<USER>\Desktop\web driver\chromedriver.exe"

        try:
            # First try: Use the specific ChromeDriver path
            if os.path.exists(chromedriver_path):
                logger.info(f"Using ChromeDriver from: {chromedriver_path}")
                service = Service(chromedriver_path)
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
            else:
                logger.warning(f"ChromeDriver not found at {chromedriver_path}")
                # Fallback: Use webdriver-manager
                logger.info("Attempting to setup ChromeDriver using webdriver-manager...")
                service = Service(ChromeDriverManager().install())
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
        except Exception as e:
            logger.warning(f"Primary ChromeDriver setup failed: {e}")
            try:
                # Final fallback: Use system ChromeDriver
                logger.info("Attempting to use system ChromeDriver...")
                self.driver = webdriver.Chrome(options=chrome_options)
            except Exception as e2:
                logger.error(f"All ChromeDriver setup methods failed: {e2}")
                raise Exception(f"Failed to initialize ChromeDriver: {e2}")

        self.wait = WebDriverWait(self.driver, PAGE_LOAD_TIMEOUT)
        logger.info("Chrome driver setup complete")
        
    def read_file_lines(self, filename):
        """Read lines from a text file and return as list"""
        try:
            with open(filename, 'r', encoding='utf-8') as file:
                lines = [line.strip() for line in file.readlines() if line.strip()]
            logger.info(f"Read {len(lines)} lines from {filename}")
            return lines
        except FileNotFoundError:
            logger.error(f"File {filename} not found!")
            return []
        except Exception as e:
            logger.error(f"Error reading {filename}: {str(e)}")
            return []
    
    def load_extension(self):
        """Load the Smart Lead Extractor extension"""
        logger.info("Loading Smart Lead Extractor extension...")
        try:
            # First, try to navigate to the extension URL
            logger.info(f"Navigating to: {EXTENSION_URL}")
            self.driver.get(EXTENSION_URL)
            time.sleep(10)  # Increased wait time for extension to load

            # Check if we successfully loaded the extension page
            current_url = self.driver.current_url
            logger.info(f"Current URL: {current_url}")

            if "chrome-extension://" not in current_url:
                logger.error("Extension URL not accessible. Please ensure:")
                logger.error("1. Smart Lead Extractor extension is installed")
                logger.error("2. Extension is enabled")
                logger.error("3. Extension ID is correct")
                return False

            # Check for and close any modal dialogs
            self.handle_modals()

            # Verify that key elements are present
            try:
                # Look for the keywords textarea
                keywords_textarea = self.driver.find_element(By.CSS_SELECTOR, "textarea[placeholder='Keywords']")
                locations_textarea = self.driver.find_element(By.CSS_SELECTOR, "textarea[placeholder='Locations']")
                logger.info("Extension interface elements found successfully")
            except Exception as e:
                logger.error(f"Extension interface elements not found: {str(e)}")
                logger.error("The extension may not be fully loaded or the interface has changed")
                return False

            logger.info("Extension loaded successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to load extension: {str(e)}")
            logger.error("Please check:")
            logger.error("1. Chrome browser is running")
            logger.error("2. Smart Lead Extractor extension is installed and enabled")
            logger.error("3. Extension URL is correct")
            return False

    def handle_modals(self):
        """Handle any modal dialogs that might appear"""
        try:
            # Check for login modal close button
            close_buttons = self.driver.find_elements(By.CSS_SELECTOR, ".close.icon, .close-icon, .close-download-modal")
            for button in close_buttons:
                if button.is_displayed():
                    button.click()
                    logger.info("Closed modal dialog")
                    time.sleep(2)
        except Exception as e:
            logger.debug(f"No modals to close: {str(e)}")
            pass
    
    def enter_keyword_and_location(self, keyword, location):
        """Enter keyword and location in the respective textareas"""
        try:
            logger.info(f"Entering keyword: '{keyword}' and location: '{location}'")

            # Check if browser session is still valid
            try:
                current_url = self.driver.current_url
                if "chrome-extension://" not in current_url:
                    logger.error("Browser session lost or not on extension page")
                    return False
            except Exception as session_error:
                logger.error(f"Browser session error: {str(session_error)}")
                return False

            # Find and clear keywords textarea
            keywords_textarea = self.wait.until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "textarea[placeholder='Keywords']"))
            )
            keywords_textarea.clear()
            keywords_textarea.send_keys(keyword)

            # Find and clear locations textarea
            locations_textarea = self.wait.until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "textarea[placeholder='Locations']"))
            )
            locations_textarea.clear()
            locations_textarea.send_keys(location)

            time.sleep(DELAY_AFTER_INPUT)  # Allow Angular to process the changes
            logger.info("Keyword and location entered successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to enter keyword and location: {str(e)}")
            # Try to reload the extension page if there's an error
            try:
                logger.info("Attempting to reload extension page...")
                self.driver.get(EXTENSION_URL)
                time.sleep(5)
                return False  # Return False to retry this combination
            except:
                logger.error("Failed to reload extension page")
                return False
    
    def start_extraction(self):
        """Click the start extraction button"""
        try:
            logger.info("Starting extraction...")
            
            # Find the start button by its text content
            start_button = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Start Extracting')]"))
            )
            start_button.click()
            
            logger.info("Extraction started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start extraction: {str(e)}")
            return False
    
    def wait_for_completion(self):
        """Wait for extraction to complete by monitoring the status"""
        logger.info("Waiting for extraction to complete...")
        start_time = time.time()
        last_results_count = 0

        try:
            while time.time() - start_time < EXTRACTION_TIMEOUT:
                try:
                    # Check if stop button is present (means extraction is running)
                    stop_button = self.driver.find_element(By.XPATH, "//button[contains(text(), 'Stop Extracting')]")
                    if stop_button:
                        # Try to get current results count for progress tracking
                        try:
                            results_element = self.driver.find_element(By.CSS_SELECTOR, ".statistic .value")
                            current_results = int(results_element.text)
                            if current_results != last_results_count:
                                logger.info(f"Extraction in progress... Found {current_results} results")
                                last_results_count = current_results
                        except:
                            logger.info("Extraction in progress...")

                        time.sleep(10)  # Check every 10 seconds
                        continue
                except NoSuchElementException:
                    # Stop button not found, extraction might be complete
                    pass

                # Check if start button is back (extraction completed)
                try:
                    start_button = self.driver.find_element(By.XPATH, "//button[contains(text(), 'Start Extracting')]")
                    if start_button:
                        # Get final results count
                        try:
                            results_element = self.driver.find_element(By.CSS_SELECTOR, ".statistic .value")
                            final_results = int(results_element.text)
                            logger.info(f"Extraction completed! Found {final_results} total results")
                        except:
                            logger.info("Extraction completed!")
                        return True
                except NoSuchElementException:
                    pass

                time.sleep(5)

            logger.warning("Extraction timeout reached")
            return False

        except Exception as e:
            logger.error(f"Error while waiting for completion: {str(e)}")
            return False
    
    def download_results(self, keyword, location):
        """Download the extracted results"""
        try:
            logger.info("Downloading results...")

            # Check if there are results to download
            try:
                results_element = self.driver.find_element(By.CSS_SELECTOR, ".statistic .value")
                results_count = int(results_element.text)
                if results_count == 0:
                    logger.warning("No results found to download")
                    return True  # Continue to next iteration
            except:
                pass

            # Find and click download button
            download_button = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Download')]"))
            )

            # Check if download button is enabled
            if "disabled" in download_button.get_attribute("class"):
                logger.warning("Download button is disabled - no data to download")
                return True

            download_button.click()

            # Wait for download to complete
            time.sleep(15)  # Increased wait time for download

            logger.info(f"Download completed for keyword: '{keyword}', location: '{location}'")
            return True

        except Exception as e:
            logger.error(f"Failed to download results: {str(e)}")
            return False
    
    def clear_data(self):
        """Clear the extracted data for next iteration"""
        try:
            logger.info("Clearing data...")
            
            # Find and click clear data button
            clear_button = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), 'Clear Data')]"))
            )
            clear_button.click()
            
            time.sleep(3)
            logger.info("Data cleared")
            return True
            
        except Exception as e:
            logger.error(f"Failed to clear data: {str(e)}")
            return False
    
    def run_automation(self):
        """Main automation workflow"""
        logger.info("Starting Smart Lead Extractor automation...")
        
        # Read keywords and locations
        self.keywords = self.read_file_lines(KEYWORDS_FILE)
        self.locations = self.read_file_lines(LOCATIONS_FILE)
        
        if not self.keywords or not self.locations:
            logger.error("No keywords or locations found. Please check your files.")
            return
        
        logger.info(f"Found {len(self.keywords)} keywords and {len(self.locations)} locations")
        
        try:
            # Setup Chrome driver
            self.setup_chrome_driver()
            
            # Load extension
            if not self.load_extension():
                return
            
            # Main automation loop
            total_combinations = len(self.keywords) * len(self.locations)
            current_combination = 0
            
            for keyword_index, keyword in enumerate(self.keywords, 1):
                logger.info(f"Processing keyword {keyword_index}/{len(self.keywords)}: '{keyword}'")
                
                for location_index, location in enumerate(self.locations, 1):
                    current_combination += 1
                    logger.info(f"Processing combination {current_combination}/{total_combinations}")
                    logger.info(f"Keyword: '{keyword}', Location: '{location}'")
                    
                    # Enter keyword and location
                    if not self.enter_keyword_and_location(keyword, location):
                        continue
                    
                    # Start extraction
                    if not self.start_extraction():
                        continue
                    
                    # Wait for completion
                    if not self.wait_for_completion():
                        logger.warning("Extraction may not have completed properly")
                    
                    # Download results
                    if not self.download_results(keyword, location):
                        continue
                    
                    # Clear data for next iteration (except for the last one)
                    if not (keyword_index == len(self.keywords) and location_index == len(self.locations)):
                        self.clear_data()
                    
                    # Small delay between iterations
                    time.sleep(5)
                
                logger.info(f"Completed all locations for keyword: '{keyword}'")
            
            logger.info("Automation completed successfully!")
            
        except Exception as e:
            logger.error(f"Automation failed: {str(e)}")
        
        finally:
            if self.driver:
                logger.info("Closing browser...")
                self.driver.quit()


def main():
    """Main function"""
    print("Smart Lead Extractor Automation")
    print("=" * 40)
    print(f"Keywords file: {KEYWORDS_FILE}")
    print(f"Locations file: {LOCATIONS_FILE}")
    print(f"Results folder: {RESULTS_FOLDER}")
    print("=" * 40)
    
    automation = SmartLeadExtractorAutomation()
    automation.run_automation()


if __name__ == "__main__":
    main()
