#!/usr/bin/env python3
"""
Smart Lead Extractor Stealth Automation Script

This script uses advanced techniques to bypass Chrome's automation detection
and directly automate the Smart Lead Extractor extension.

Requirements:
- Chrome browser with Smart Lead Extractor extension installed
- Python packages: selenium, undetected-chromedriver
- keywords.txt and location.txt files in the same directory
"""

import os
import time
import logging
from datetime import datetime
import random
import json

# Try to import undetected-chromedriver for stealth mode
try:
    import undetected_chromedriver as uc
    STEALTH_AVAILABLE = True
except ImportError:
    STEALTH_AVAILABLE = False
    from selenium import webdriver
    from selenium.webdriver.chrome.service import Service
    from selenium.webdriver.chrome.options import Options

from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains

# Configuration
EXTENSION_URL = "chrome-extension://dlfcpdjocjjmobbdljgjlmiapgipaobf/views/index.html"
KEYWORDS_FILE = "keywords.txt"
LOCATIONS_FILE = "location.txt"
RESULTS_FOLDER = "extracted_data"
CHROMEDRIVER_PATH = r"C:\Users\<USER>\Desktop\web driver\chromedriver.exe"

# Stealth settings
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
]

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('stealth_automation.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class StealthSmartLeadExtractor:
    def __init__(self):
        self.driver = None
        self.wait = None
        self.keywords = []
        self.locations = []
        
    def setup_stealth_driver(self):
        """Setup Chrome driver with advanced stealth techniques"""
        logger.info("Setting up stealth Chrome driver...")
        
        if STEALTH_AVAILABLE:
            # Use undetected-chromedriver for maximum stealth
            options = uc.ChromeOptions()
            
            # Advanced stealth options
            options.add_argument("--no-first-run")
            options.add_argument("--no-service-autorun")
            options.add_argument("--password-store=basic")
            options.add_argument("--disable-blink-features=AutomationControlled")
            options.add_argument("--disable-features=VizDisplayCompositor")
            options.add_argument("--disable-ipc-flooding-protection")
            options.add_argument("--disable-renderer-backgrounding")
            options.add_argument("--disable-backgrounding-occluded-windows")
            options.add_argument("--disable-client-side-phishing-detection")
            options.add_argument("--disable-crash-reporter")
            options.add_argument("--disable-oopr-debug-crash-dump")
            options.add_argument("--no-crash-upload")
            options.add_argument("--disable-gpu")
            options.add_argument("--disable-extensions-file-access-check")
            options.add_argument("--disable-extensions-http-throttling")
            options.add_argument("--disable-component-extensions-with-background-pages")
            options.add_argument("--disable-default-apps")
            options.add_argument("--mute-audio")
            options.add_argument("--no-default-browser-check")
            options.add_argument("--autoplay-policy=user-gesture-required")
            options.add_argument("--disable-background-timer-throttling")
            options.add_argument("--disable-renderer-backgrounding")
            options.add_argument("--disable-backgrounding-occluded-windows")
            options.add_argument("--disable-restore-session-state")
            options.add_argument("--disable-translate")
            options.add_argument("--hide-scrollbars")
            options.add_argument("--disable-web-security")
            options.add_argument("--allow-running-insecure-content")
            options.add_argument("--disable-features=TranslateUI")
            options.add_argument("--disable-features=BlinkGenPropertyTrees")
            
            # Set random user agent
            user_agent = random.choice(USER_AGENTS)
            options.add_argument(f"--user-agent={user_agent}")
            
            # Download settings
            download_dir = os.path.join(os.getcwd(), RESULTS_FOLDER)
            os.makedirs(download_dir, exist_ok=True)
            
            prefs = {
                "download.default_directory": download_dir,
                "download.prompt_for_download": False,
                "download.directory_upgrade": True,
                "safebrowsing.enabled": True,
                "profile.default_content_setting_values.notifications": 2,
                "profile.default_content_settings.popups": 0,
                "profile.managed_default_content_settings.images": 2
            }
            options.add_experimental_option("prefs", prefs)
            
            # Create driver with stealth
            if os.path.exists(CHROMEDRIVER_PATH):
                self.driver = uc.Chrome(driver_executable_path=CHROMEDRIVER_PATH, options=options, version_main=None)
            else:
                self.driver = uc.Chrome(options=options, version_main=None)
                
        else:
            logger.warning("undetected-chromedriver not available, using standard selenium with stealth options")
            # Fallback to standard selenium with maximum stealth
            options = Options()
            
            # All the stealth arguments
            stealth_args = [
                "--no-sandbox",
                "--disable-dev-shm-usage",
                "--disable-blink-features=AutomationControlled",
                "--disable-features=VizDisplayCompositor",
                "--disable-ipc-flooding-protection",
                "--disable-renderer-backgrounding",
                "--disable-backgrounding-occluded-windows",
                "--disable-client-side-phishing-detection",
                "--disable-crash-reporter",
                "--disable-oopr-debug-crash-dump",
                "--no-crash-upload",
                "--disable-gpu",
                "--disable-extensions-file-access-check",
                "--disable-extensions-http-throttling",
                "--disable-component-extensions-with-background-pages",
                "--disable-default-apps",
                "--mute-audio",
                "--no-default-browser-check",
                "--autoplay-policy=user-gesture-required",
                "--disable-background-timer-throttling",
                "--disable-renderer-backgrounding",
                "--disable-backgrounding-occluded-windows",
                "--disable-restore-session-state",
                "--disable-translate",
                "--hide-scrollbars",
                "--disable-web-security",
                "--allow-running-insecure-content",
                "--disable-features=TranslateUI",
                "--disable-features=BlinkGenPropertyTrees"
            ]
            
            for arg in stealth_args:
                options.add_argument(arg)
            
            # Remove automation indicators
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)
            
            # Set random user agent
            user_agent = random.choice(USER_AGENTS)
            options.add_argument(f"--user-agent={user_agent}")
            
            # Download settings
            download_dir = os.path.join(os.getcwd(), RESULTS_FOLDER)
            os.makedirs(download_dir, exist_ok=True)
            
            prefs = {
                "download.default_directory": download_dir,
                "download.prompt_for_download": False,
                "download.directory_upgrade": True,
                "safebrowsing.enabled": True,
                "profile.default_content_setting_values.notifications": 2,
                "profile.default_content_settings.popups": 0,
                "profile.managed_default_content_settings.images": 2
            }
            options.add_experimental_option("prefs", prefs)
            
            # Use ChromeDriver
            if os.path.exists(CHROMEDRIVER_PATH):
                service = Service(CHROMEDRIVER_PATH)
                self.driver = webdriver.Chrome(service=service, options=options)
            else:
                self.driver = webdriver.Chrome(options=options)
        
        # Execute stealth scripts to hide automation
        self.execute_stealth_scripts()
        
        self.wait = WebDriverWait(self.driver, 30)
        logger.info("Stealth Chrome driver setup complete")
        
    def execute_stealth_scripts(self):
        """Execute JavaScript to hide automation traces"""
        stealth_scripts = [
            # Remove webdriver property
            "Object.defineProperty(navigator, 'webdriver', {get: () => undefined})",
            
            # Override the plugins property to use a custom getter
            """
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5]
            });
            """,
            
            # Override the languages property to remove automation indicators
            """
            Object.defineProperty(navigator, 'languages', {
                get: () => ['en-US', 'en']
            });
            """,
            
            # Override chrome property
            """
            window.chrome = {
                runtime: {},
                loadTimes: function() {},
                csi: function() {},
                app: {}
            };
            """,
            
            # Override permissions
            """
            const originalQuery = window.navigator.permissions.query;
            return window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
            );
            """,
            
            # Remove automation from user agent
            """
            Object.defineProperty(navigator, 'userAgent', {
                get: () => navigator.userAgent.replace('HeadlessChrome', 'Chrome')
            });
            """
        ]
        
        for script in stealth_scripts:
            try:
                self.driver.execute_script(script)
            except Exception as e:
                logger.debug(f"Stealth script execution failed: {e}")
    
    def human_like_delay(self, min_delay=1, max_delay=3):
        """Add human-like random delays"""
        delay = random.uniform(min_delay, max_delay)
        time.sleep(delay)
    
    def human_like_typing(self, element, text, typing_speed=0.1):
        """Type text with human-like speed and occasional pauses"""
        element.clear()
        for char in text:
            element.send_keys(char)
            # Random typing speed with occasional longer pauses
            if random.random() < 0.1:  # 10% chance of longer pause
                time.sleep(random.uniform(0.3, 0.8))
            else:
                time.sleep(random.uniform(0.05, typing_speed))
    
    def read_file_lines(self, filename):
        """Read lines from a text file and return as list"""
        try:
            with open(filename, 'r', encoding='utf-8') as file:
                lines = [line.strip() for line in file.readlines() if line.strip()]
            logger.info(f"Read {len(lines)} lines from {filename}")
            return lines
        except FileNotFoundError:
            logger.error(f"File {filename} not found!")
            return []
        except Exception as e:
            logger.error(f"Error reading {filename}: {str(e)}")
            return []
    
    def access_extension_with_stealth(self):
        """Access the extension using stealth techniques"""
        logger.info("Attempting to access extension with stealth techniques...")
        
        # First, navigate to a regular page to establish session
        self.driver.get("https://www.google.com")
        self.human_like_delay(2, 4)
        
        # Execute additional stealth scripts after page load
        self.execute_stealth_scripts()
        
        # Now try to access the extension
        logger.info(f"Navigating to extension URL: {EXTENSION_URL}")
        self.driver.get(EXTENSION_URL)
        self.human_like_delay(5, 8)
        
        # Check if we successfully accessed the extension
        current_url = self.driver.current_url
        logger.info(f"Current URL: {current_url}")
        
        if "chrome-extension://" in current_url:
            logger.info("Successfully accessed extension!")
            return True
        else:
            logger.error("Failed to access extension")
            return False

    def find_element_with_retry(self, by, value, max_retries=5):
        """Find element with multiple retry attempts and different strategies"""
        for attempt in range(max_retries):
            try:
                element = self.wait.until(EC.presence_of_element_located((by, value)))
                return element
            except TimeoutException:
                logger.warning(f"Attempt {attempt + 1} failed to find element: {value}")
                if attempt < max_retries - 1:
                    self.human_like_delay(2, 4)
                    # Try refreshing the page
                    if attempt == 2:
                        logger.info("Refreshing page and retrying...")
                        self.driver.refresh()
                        self.human_like_delay(5, 8)
        return None

    def enter_data_stealthily(self, keyword, location):
        """Enter keyword and location data using stealth techniques"""
        logger.info(f"Entering data stealthily - Keyword: '{keyword}', Location: '{location}'")

        # Multiple selectors to try for keywords textarea
        keyword_selectors = [
            (By.CSS_SELECTOR, "textarea[placeholder='Keywords']"),
            (By.CSS_SELECTOR, "textarea[ng-model='local.keywordTextarea']"),
            (By.XPATH, "//textarea[contains(@placeholder, 'Keywords')]"),
            (By.XPATH, "//textarea[contains(@ng-model, 'keyword')]")
        ]

        # Multiple selectors to try for locations textarea
        location_selectors = [
            (By.CSS_SELECTOR, "textarea[placeholder='Locations']"),
            (By.CSS_SELECTOR, "textarea[ng-model='local.locationTextarea']"),
            (By.XPATH, "//textarea[contains(@placeholder, 'Locations')]"),
            (By.XPATH, "//textarea[contains(@ng-model, 'location')]")
        ]

        # Try to find and fill keywords textarea
        keywords_element = None
        for by, selector in keyword_selectors:
            keywords_element = self.find_element_with_retry(by, selector, max_retries=3)
            if keywords_element:
                logger.info(f"Found keywords textarea with selector: {selector}")
                break

        if not keywords_element:
            logger.error("Could not find keywords textarea")
            return False

        # Try to find and fill locations textarea
        locations_element = None
        for by, selector in location_selectors:
            locations_element = self.find_element_with_retry(by, selector, max_retries=3)
            if locations_element:
                logger.info(f"Found locations textarea with selector: {selector}")
                break

        if not locations_element:
            logger.error("Could not find locations textarea")
            return False

        # Human-like interaction with keywords field
        try:
            # Click on the element first
            ActionChains(self.driver).move_to_element(keywords_element).click().perform()
            self.human_like_delay(0.5, 1.5)

            # Clear and type with human-like behavior
            keywords_element.clear()
            self.human_like_delay(0.5, 1)
            self.human_like_typing(keywords_element, keyword)

            logger.info("Keywords entered successfully")
        except Exception as e:
            logger.error(f"Failed to enter keywords: {e}")
            return False

        # Human-like interaction with locations field
        try:
            # Click on the element first
            ActionChains(self.driver).move_to_element(locations_element).click().perform()
            self.human_like_delay(0.5, 1.5)

            # Clear and type with human-like behavior
            locations_element.clear()
            self.human_like_delay(0.5, 1)
            self.human_like_typing(locations_element, location)

            logger.info("Location entered successfully")
        except Exception as e:
            logger.error(f"Failed to enter location: {e}")
            return False

        # Wait for Angular to process
        self.human_like_delay(2, 4)
        return True

    def start_extraction_stealthily(self):
        """Start the extraction process using stealth techniques"""
        logger.info("Starting extraction stealthily...")

        # Multiple selectors for start button
        start_selectors = [
            (By.XPATH, "//button[contains(text(), 'Start Extracting')]"),
            (By.XPATH, "//button[contains(text(), 'Start')]"),
            (By.CSS_SELECTOR, "button[ng-click*='start']"),
            (By.XPATH, "//button[contains(@ng-click, 'start')]")
        ]

        start_button = None
        for by, selector in start_selectors:
            start_button = self.find_element_with_retry(by, selector, max_retries=3)
            if start_button:
                logger.info(f"Found start button with selector: {selector}")
                break

        if not start_button:
            logger.error("Could not find start button")
            return False

        try:
            # Human-like click
            ActionChains(self.driver).move_to_element(start_button).pause(random.uniform(0.5, 1.5)).click().perform()
            logger.info("Start button clicked successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to click start button: {e}")
            return False

    def wait_for_extraction_completion(self):
        """Wait for extraction to complete with stealth monitoring"""
        logger.info("Waiting for extraction to complete...")
        start_time = time.time()
        timeout = 600  # 10 minutes

        while time.time() - start_time < timeout:
            try:
                # Check for stop button (means extraction is running)
                stop_button = self.driver.find_element(By.XPATH, "//button[contains(text(), 'Stop')]")
                if stop_button and stop_button.is_displayed():
                    logger.info("Extraction in progress...")
                    self.human_like_delay(10, 15)
                    continue
            except NoSuchElementException:
                pass

            try:
                # Check for start button (means extraction completed)
                start_button = self.driver.find_element(By.XPATH, "//button[contains(text(), 'Start')]")
                if start_button and start_button.is_displayed():
                    logger.info("Extraction completed!")
                    return True
            except NoSuchElementException:
                pass

            # Random delay to avoid detection
            self.human_like_delay(5, 10)

        logger.warning("Extraction timeout reached")
        return False

    def download_results_stealthily(self):
        """Download results using stealth techniques"""
        logger.info("Downloading results stealthily...")

        # Multiple selectors for download button
        download_selectors = [
            (By.XPATH, "//button[contains(text(), 'Download')]"),
            (By.CSS_SELECTOR, "button[ng-click*='download']"),
            (By.XPATH, "//button[contains(@ng-click, 'download')]")
        ]

        download_button = None
        for by, selector in download_selectors:
            download_button = self.find_element_with_retry(by, selector, max_retries=3)
            if download_button:
                logger.info(f"Found download button with selector: {selector}")
                break

        if not download_button:
            logger.error("Could not find download button")
            return False

        try:
            # Check if button is enabled
            if "disabled" in download_button.get_attribute("class"):
                logger.warning("Download button is disabled - no data to download")
                return True

            # Human-like click
            ActionChains(self.driver).move_to_element(download_button).pause(random.uniform(0.5, 1.5)).click().perform()
            logger.info("Download button clicked successfully")

            # Wait for download to complete
            self.human_like_delay(10, 20)
            return True
        except Exception as e:
            logger.error(f"Failed to click download button: {e}")
            return False

    def clear_data_stealthily(self):
        """Clear data using stealth techniques"""
        logger.info("Clearing data stealthily...")

        # Multiple selectors for clear button
        clear_selectors = [
            (By.XPATH, "//button[contains(text(), 'Clear')]"),
            (By.CSS_SELECTOR, "button[ng-click*='clear']"),
            (By.XPATH, "//button[contains(@ng-click, 'clear')]")
        ]

        clear_button = None
        for by, selector in clear_selectors:
            clear_button = self.find_element_with_retry(by, selector, max_retries=3)
            if clear_button:
                logger.info(f"Found clear button with selector: {selector}")
                break

        if not clear_button:
            logger.warning("Could not find clear button")
            return True  # Continue anyway

        try:
            # Human-like click
            ActionChains(self.driver).move_to_element(clear_button).pause(random.uniform(0.5, 1.5)).click().perform()
            logger.info("Clear button clicked successfully")
            self.human_like_delay(2, 4)
            return True
        except Exception as e:
            logger.error(f"Failed to click clear button: {e}")
            return True  # Continue anyway

    def run_stealth_automation(self):
        """Main stealth automation workflow"""
        logger.info("Starting stealth automation for Smart Lead Extractor...")

        # Read keywords and locations
        self.keywords = self.read_file_lines(KEYWORDS_FILE)
        self.locations = self.read_file_lines(LOCATIONS_FILE)

        if not self.keywords or not self.locations:
            logger.error("No keywords or locations found. Please check your files.")
            return

        logger.info(f"Found {len(self.keywords)} keywords and {len(self.locations)} locations")

        try:
            # Setup stealth driver
            self.setup_stealth_driver()

            # Access extension with stealth
            if not self.access_extension_with_stealth():
                logger.error("Failed to access extension. Automation cannot continue.")
                return

            # Main automation loop
            total_combinations = len(self.keywords) * len(self.locations)
            current_combination = 0
            successful_extractions = 0
            failed_extractions = 0

            logger.info(f"Starting extraction for {total_combinations} combinations")

            for keyword_index, keyword in enumerate(self.keywords, 1):
                logger.info(f"Processing keyword {keyword_index}/{len(self.keywords)}: '{keyword}'")

                for location_index, location in enumerate(self.locations, 1):
                    current_combination += 1
                    logger.info(f"Processing combination {current_combination}/{total_combinations}")
                    logger.info(f"Keyword: '{keyword}', Location: '{location}'")

                    # Add random delay between combinations to avoid detection
                    if current_combination > 1:
                        self.human_like_delay(30, 60)  # 30-60 seconds between combinations

                    try:
                        # Enter keyword and location
                        if not self.enter_data_stealthily(keyword, location):
                            logger.error("Failed to enter data, skipping this combination")
                            failed_extractions += 1
                            continue

                        # Start extraction
                        if not self.start_extraction_stealthily():
                            logger.error("Failed to start extraction, skipping this combination")
                            failed_extractions += 1
                            continue

                        # Wait for completion
                        if not self.wait_for_extraction_completion():
                            logger.warning("Extraction may not have completed properly")

                        # Download results
                        if not self.download_results_stealthily():
                            logger.error("Failed to download results")
                            failed_extractions += 1
                        else:
                            successful_extractions += 1
                            logger.info(f"Successfully completed combination {current_combination}")

                        # Clear data for next iteration (except for the last one)
                        if not (keyword_index == len(self.keywords) and location_index == len(self.locations)):
                            self.clear_data_stealthily()

                    except Exception as e:
                        logger.error(f"Error processing combination {current_combination}: {e}")
                        failed_extractions += 1

                        # Try to recover by refreshing the page
                        try:
                            logger.info("Attempting to recover by refreshing extension...")
                            self.driver.refresh()
                            self.human_like_delay(5, 10)
                        except:
                            logger.error("Recovery failed")

                logger.info(f"Completed all locations for keyword: '{keyword}'")

                # Longer delay between keywords
                if keyword_index < len(self.keywords):
                    logger.info("Taking a longer break between keywords...")
                    self.human_like_delay(120, 300)  # 2-5 minutes between keywords

            # Final summary
            logger.info("="*60)
            logger.info("STEALTH AUTOMATION COMPLETED!")
            logger.info(f"Total combinations processed: {total_combinations}")
            logger.info(f"Successful extractions: {successful_extractions}")
            logger.info(f"Failed extractions: {failed_extractions}")
            logger.info(f"Success rate: {(successful_extractions/total_combinations)*100:.1f}%")
            logger.info(f"Check the '{RESULTS_FOLDER}' folder for downloaded files")
            logger.info("="*60)

        except Exception as e:
            logger.error(f"Stealth automation failed: {str(e)}")

        finally:
            # Keep browser open for a while to avoid suspicion
            logger.info("Keeping browser open for a few minutes to avoid detection...")
            self.human_like_delay(180, 300)  # 3-5 minutes

            if self.driver:
                logger.info("Closing browser...")
                self.driver.quit()


def install_stealth_dependencies():
    """Install required dependencies for stealth mode"""
    try:
        import subprocess
        import sys

        logger.info("Installing stealth dependencies...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "undetected-chromedriver"])
        logger.info("Stealth dependencies installed successfully")
        return True
    except Exception as e:
        logger.error(f"Failed to install stealth dependencies: {e}")
        return False


def main():
    """Main function"""
    print("Smart Lead Extractor - STEALTH Automation")
    print("=" * 50)
    print(f"Keywords file: {KEYWORDS_FILE}")
    print(f"Locations file: {LOCATIONS_FILE}")
    print(f"Results folder: {RESULTS_FOLDER}")
    print("=" * 50)
    print()

    if not STEALTH_AVAILABLE:
        print("⚠️  STEALTH MODE NOT AVAILABLE")
        print("Installing undetected-chromedriver for maximum stealth...")
        if install_stealth_dependencies():
            print("✅ Stealth dependencies installed. Please restart the script.")
            return
        else:
            print("❌ Failed to install stealth dependencies.")
            print("Continuing with standard selenium (less stealthy)...")
            print()

    print("🥷 STEALTH MODE ACTIVATED")
    print("This script uses advanced techniques to bypass Chrome automation detection")
    print("=" * 50)

    automation = StealthSmartLeadExtractor()
    automation.run_stealth_automation()


if __name__ == "__main__":
    main()
