#!/usr/bin/env python3
"""
Stealth Diagnostic Script for Smart Lead Extractor

This script uses stealth techniques to access the extension and diagnose
what elements are actually available on the page.
"""

import os
import time
import logging
import random
import undetected_chromedriver as uc
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC

# Configuration
EXTENSION_URL = "chrome-extension://dlfcpdjocjjmobbdljgjlmiapgipaobf/views/index.html"
CHROMEDRIVER_PATH = r"C:\Users\<USER>\Desktop\web driver\chromedriver.exe"

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def setup_stealth_driver():
    """Setup stealth Chrome driver"""
    logger.info("Setting up stealth Chrome driver...")
    
    options = uc.ChromeOptions()
    options.add_argument("--no-first-run")
    options.add_argument("--no-service-autorun")
    options.add_argument("--password-store=basic")
    options.add_argument("--disable-blink-features=AutomationControlled")
    options.add_argument("--disable-features=VizDisplayCompositor")
    options.add_argument("--disable-extensions-file-access-check")
    options.add_argument("--disable-extensions-http-throttling")
    options.add_argument("--disable-web-security")
    options.add_argument("--allow-running-insecure-content")
    
    # Random user agent
    user_agents = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    ]
    options.add_argument(f"--user-agent={random.choice(user_agents)}")
    
    if os.path.exists(CHROMEDRIVER_PATH):
        driver = uc.Chrome(driver_executable_path=CHROMEDRIVER_PATH, options=options, version_main=None)
    else:
        driver = uc.Chrome(options=options, version_main=None)
    
    return driver

def execute_stealth_scripts(driver):
    """Execute stealth scripts"""
    stealth_scripts = [
        "Object.defineProperty(navigator, 'webdriver', {get: () => undefined})",
        "Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]})",
        "Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en']})",
        "window.chrome = {runtime: {}, loadTimes: function() {}, csi: function() {}, app: {}};"
    ]
    
    for script in stealth_scripts:
        try:
            driver.execute_script(script)
        except:
            pass

def diagnose_extension_interface(driver):
    """Diagnose the extension interface"""
    logger.info("Diagnosing extension interface...")
    
    # Navigate to Google first
    driver.get("https://www.google.com")
    time.sleep(3)
    execute_stealth_scripts(driver)
    
    # Now navigate to extension
    logger.info(f"Navigating to: {EXTENSION_URL}")
    driver.get(EXTENSION_URL)
    time.sleep(10)  # Wait for extension to load
    
    current_url = driver.current_url
    logger.info(f"Current URL: {current_url}")
    
    if "chrome-extension://" not in current_url:
        logger.error("Failed to access extension")
        return False
    
    logger.info("✅ Successfully accessed extension!")
    
    # Save page source
    try:
        page_source = driver.page_source
        with open("stealth_extension_source.html", "w", encoding="utf-8") as f:
            f.write(page_source)
        logger.info("📄 Page source saved to 'stealth_extension_source.html'")
    except Exception as e:
        logger.error(f"Failed to save page source: {e}")
    
    # Find all textarea elements
    try:
        textareas = driver.find_elements(By.TAG_NAME, "textarea")
        logger.info(f"Found {len(textareas)} textarea elements:")
        for i, textarea in enumerate(textareas):
            placeholder = textarea.get_attribute("placeholder")
            ng_model = textarea.get_attribute("ng-model")
            class_name = textarea.get_attribute("class")
            id_attr = textarea.get_attribute("id")
            logger.info(f"  Textarea {i+1}: placeholder='{placeholder}', ng-model='{ng_model}', class='{class_name}', id='{id_attr}'")
    except Exception as e:
        logger.error(f"Error finding textareas: {e}")
    
    # Find all button elements
    try:
        buttons = driver.find_elements(By.TAG_NAME, "button")
        logger.info(f"Found {len(buttons)} button elements:")
        for i, button in enumerate(buttons):
            text = button.text.strip()
            ng_click = button.get_attribute("ng-click")
            class_name = button.get_attribute("class")
            id_attr = button.get_attribute("id")
            if text or ng_click:
                logger.info(f"  Button {i+1}: text='{text}', ng-click='{ng_click}', class='{class_name}', id='{id_attr}'")
    except Exception as e:
        logger.error(f"Error finding buttons: {e}")
    
    # Find all input elements
    try:
        inputs = driver.find_elements(By.TAG_NAME, "input")
        logger.info(f"Found {len(inputs)} input elements:")
        for i, input_elem in enumerate(inputs):
            input_type = input_elem.get_attribute("type")
            placeholder = input_elem.get_attribute("placeholder")
            ng_model = input_elem.get_attribute("ng-model")
            class_name = input_elem.get_attribute("class")
            id_attr = input_elem.get_attribute("id")
            logger.info(f"  Input {i+1}: type='{input_type}', placeholder='{placeholder}', ng-model='{ng_model}', class='{class_name}', id='{id_attr}'")
    except Exception as e:
        logger.error(f"Error finding inputs: {e}")
    
    # Try to find elements by various selectors
    selectors_to_try = [
        ("CSS", "textarea"),
        ("CSS", "input[type='text']"),
        ("CSS", "[ng-model*='keyword']"),
        ("CSS", "[ng-model*='location']"),
        ("CSS", "[placeholder*='keyword']"),
        ("CSS", "[placeholder*='location']"),
        ("XPATH", "//textarea"),
        ("XPATH", "//input[@type='text']"),
        ("XPATH", "//*[contains(@ng-model, 'keyword')]"),
        ("XPATH", "//*[contains(@ng-model, 'location')]"),
        ("XPATH", "//*[contains(@placeholder, 'keyword')]"),
        ("XPATH", "//*[contains(@placeholder, 'location')]")
    ]
    
    logger.info("Testing various selectors:")
    for selector_type, selector in selectors_to_try:
        try:
            if selector_type == "CSS":
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
            else:
                elements = driver.find_elements(By.XPATH, selector)
            
            if elements:
                logger.info(f"  ✅ {selector_type}: '{selector}' found {len(elements)} elements")
            else:
                logger.info(f"  ❌ {selector_type}: '{selector}' found 0 elements")
        except Exception as e:
            logger.info(f"  ❌ {selector_type}: '{selector}' error: {e}")
    
    # Wait for user to manually inspect
    logger.info("\n" + "="*60)
    logger.info("MANUAL INSPECTION")
    logger.info("="*60)
    logger.info("The browser will stay open for manual inspection.")
    logger.info("Please check the extension interface and note:")
    logger.info("1. Are there text input fields visible?")
    logger.info("2. What are their actual attributes?")
    logger.info("3. Are there any buttons visible?")
    logger.info("4. Check the browser console for any errors")
    logger.info("="*60)
    
    input("Press ENTER when you've finished inspecting...")
    
    return True

def main():
    """Main function"""
    print("Smart Lead Extractor - Stealth Diagnostic")
    print("=" * 50)
    
    driver = None
    try:
        driver = setup_stealth_driver()
        diagnose_extension_interface(driver)
        
    except Exception as e:
        logger.error(f"Diagnostic failed: {e}")
    
    finally:
        if driver:
            logger.info("Closing browser...")
            driver.quit()

if __name__ == "__main__":
    main()
