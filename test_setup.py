#!/usr/bin/env python3
"""
Test script to verify the Smart Lead Extractor automation setup

This script checks if all dependencies and files are properly configured
before running the main automation.
"""

import os
import sys

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ Python 3.7+ is required")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro}")
    return True

def check_dependencies():
    """Check if required packages are installed"""
    try:
        import selenium
        print(f"✅ Selenium {selenium.__version__}")
    except ImportError:
        print("❌ Selenium not installed. Run: pip install selenium")
        return False
    
    try:
        import webdriver_manager
        print(f"✅ WebDriver Manager installed")
    except ImportError:
        print("❌ WebDriver Manager not installed. Run: pip install webdriver-manager")
        return False
    
    return True

def check_files():
    """Check if required files exist"""
    files_to_check = [
        'keywords.txt',
        'location.txt',
        'smart_lead_extractor_automation.py'
    ]
    
    all_files_exist = True
    for file in files_to_check:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} not found")
            all_files_exist = False
    
    return all_files_exist

def check_file_contents():
    """Check if data files have content"""
    try:
        with open('keywords.txt', 'r') as f:
            keywords = [line.strip() for line in f if line.strip()]
        print(f"✅ Keywords file: {len(keywords)} keywords found")
        
        with open('location.txt', 'r') as f:
            locations = [line.strip() for line in f if line.strip()]
        print(f"✅ Locations file: {len(locations)} locations found")
        
        if keywords and locations:
            total_combinations = len(keywords) * len(locations)
            print(f"📊 Total combinations to process: {total_combinations}")
            return True
        else:
            print("❌ Empty data files")
            return False
            
    except Exception as e:
        print(f"❌ Error reading data files: {e}")
        return False

def test_chrome_driver():
    """Test if Chrome driver can be initialized"""
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.service import Service
        from selenium.webdriver.chrome.options import Options
        from webdriver_manager.chrome import ChromeDriverManager
        
        print("🔄 Testing Chrome driver setup...")
        
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # Test basic functionality
        driver.get("https://www.google.com")
        title = driver.title
        driver.quit()
        
        print("✅ Chrome driver test successful")
        return True
        
    except Exception as e:
        print(f"❌ Chrome driver test failed: {e}")
        print("   Make sure Chrome browser is installed")
        return False

def main():
    """Run all tests"""
    print("Smart Lead Extractor - Setup Test")
    print("=" * 40)
    
    tests = [
        ("Python Version", check_python_version),
        ("Dependencies", check_dependencies),
        ("Required Files", check_files),
        ("File Contents", check_file_contents),
        ("Chrome Driver", test_chrome_driver)
    ]
    
    all_passed = True
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        if not test_func():
            all_passed = False
    
    print("\n" + "=" * 40)
    if all_passed:
        print("🎉 All tests passed! You can run the automation script.")
        print("   Run: python smart_lead_extractor_automation.py")
    else:
        print("❌ Some tests failed. Please fix the issues before running the automation.")
    
    print("=" * 40)

if __name__ == "__main__":
    main()
