#!/usr/bin/env python3
"""
Test script to verify Smart Lead Extractor extension accessibility

This script opens Chrome and tries to access the extension to verify it's working.
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options

# Extension URL
EXTENSION_URL = "chrome-extension://dlfcpdjocjjmobbdljgjlmiapgipaobf/views/index.html"
CHROMEDRIVER_PATH = r"C:\Users\<USER>\Desktop\web driver\chromedriver.exe"

def test_extension_access():
    """Test if the extension is accessible"""
    driver = None
    try:
        print("🔄 Setting up Chrome driver...")
        
        # Setup Chrome options to allow extension access
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_argument("--disable-web-security")
        chrome_options.add_argument("--allow-running-insecure-content")
        chrome_options.add_argument("--disable-features=VizDisplayCompositor")
        chrome_options.add_argument("--disable-extensions-file-access-check")
        chrome_options.add_argument("--disable-extensions-http-throttling")
        chrome_options.add_argument("--allow-chrome-scheme-url")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        chrome_options.add_experimental_option("detach", True)
        
        # Use the specific ChromeDriver path
        if os.path.exists(CHROMEDRIVER_PATH):
            print(f"   Using ChromeDriver from: {CHROMEDRIVER_PATH}")
            service = Service(CHROMEDRIVER_PATH)
            driver = webdriver.Chrome(service=service, options=chrome_options)
        else:
            print(f"   ChromeDriver not found at {CHROMEDRIVER_PATH}")
            return False
        
        print("✅ Chrome driver initialized")
        
        # Try to access the extension
        print(f"🔄 Accessing extension URL: {EXTENSION_URL}")
        driver.get(EXTENSION_URL)
        time.sleep(10)  # Wait for extension to load
        
        # Check current URL
        current_url = driver.current_url
        print(f"   Current URL: {current_url}")
        
        if "chrome-extension://" not in current_url:
            print("❌ Extension not accessible")
            print("   Possible issues:")
            print("   1. Extension not installed")
            print("   2. Extension not enabled")
            print("   3. Wrong extension ID")
            return False
        
        print("✅ Extension URL accessible")
        
        # Wait longer for the extension to fully load
        print("🔄 Waiting for extension to fully load...")
        time.sleep(15)  # Wait longer for Angular/JavaScript to load

        # Save page source for debugging
        try:
            page_source = driver.page_source
            with open("extension_page_source.html", "w", encoding="utf-8") as f:
                f.write(page_source)
            print("📄 Page source saved to 'extension_page_source.html' for debugging")
        except Exception as e:
            print(f"⚠️ Could not save page source: {e}")

        # Check for key interface elements with multiple selectors
        print("🔄 Checking interface elements...")

        # Try multiple selectors for keywords textarea
        keywords_found = False
        keywords_selectors = [
            "textarea[placeholder='Keywords']",
            "textarea[ng-model='local.keywordTextarea']",
            "textarea:contains('Keywords')",
            "textarea"
        ]

        for selector in keywords_selectors:
            try:
                if "contains" in selector:
                    continue  # Skip XPath-style selectors for now
                keywords_textarea = driver.find_element(By.CSS_SELECTOR, selector)
                print(f"✅ Keywords textarea found with selector: {selector}")
                keywords_found = True
                break
            except:
                continue

        if not keywords_found:
            print("❌ Keywords textarea not found with any selector")
            # Try to find any textarea elements
            try:
                textareas = driver.find_elements(By.TAG_NAME, "textarea")
                print(f"   Found {len(textareas)} textarea elements total")
                for i, textarea in enumerate(textareas):
                    placeholder = textarea.get_attribute("placeholder")
                    ng_model = textarea.get_attribute("ng-model")
                    print(f"   Textarea {i+1}: placeholder='{placeholder}', ng-model='{ng_model}'")
            except Exception as e:
                print(f"   Could not find any textarea elements: {e}")

        # Try multiple selectors for locations textarea
        locations_found = False
        locations_selectors = [
            "textarea[placeholder='Locations']",
            "textarea[ng-model='local.locationTextarea']"
        ]

        for selector in locations_selectors:
            try:
                locations_textarea = driver.find_element(By.CSS_SELECTOR, selector)
                print(f"✅ Locations textarea found with selector: {selector}")
                locations_found = True
                break
            except:
                continue

        if not locations_found:
            print("❌ Locations textarea not found with any selector")

        # Try multiple selectors for start button
        start_found = False
        start_selectors = [
            "//button[contains(text(), 'Start Extracting')]",
            "//button[contains(text(), 'Start')]",
            "button[ng-click*='start']"
        ]

        for selector in start_selectors:
            try:
                if selector.startswith("//"):
                    start_button = driver.find_element(By.XPATH, selector)
                else:
                    start_button = driver.find_element(By.CSS_SELECTOR, selector)
                print(f"✅ Start button found with selector: {selector}")
                start_found = True
                break
            except:
                continue

        if not start_found:
            print("❌ Start button not found with any selector")
            # Try to find any button elements
            try:
                buttons = driver.find_elements(By.TAG_NAME, "button")
                print(f"   Found {len(buttons)} button elements total")
                for i, button in enumerate(buttons):
                    text = button.text.strip()
                    ng_click = button.get_attribute("ng-click")
                    if text or ng_click:
                        print(f"   Button {i+1}: text='{text}', ng-click='{ng_click}'")
            except Exception as e:
                print(f"   Could not find any button elements: {e}")

        # Check if we found the essential elements
        if keywords_found and locations_found and start_found:
            print("🎉 Essential interface elements found! Extension is ready for automation.")
            return True
        else:
            print("❌ Some essential elements not found. Check the saved page source.")
            return False
        
        print("🎉 All interface elements found! Extension is ready for automation.")
        
        # Keep browser open for manual inspection
        print("\n📋 Manual verification:")
        print("   - Browser will stay open for 30 seconds")
        print("   - Please verify the extension interface manually")
        print("   - Check if you can enter text in the textareas")
        
        time.sleep(30)
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False
    
    finally:
        if driver:
            print("🔄 Closing browser...")
            driver.quit()

def main():
    """Main function"""
    print("Smart Lead Extractor - Extension Test")
    print("=" * 50)
    
    if test_extension_access():
        print("\n🎉 Extension test PASSED!")
        print("   You can now run the main automation script.")
    else:
        print("\n❌ Extension test FAILED!")
        print("   Please fix the issues before running automation.")
        print("\n🔧 Troubleshooting steps:")
        print("   1. Install Smart Lead Extractor extension in Chrome")
        print("   2. Enable the extension")
        print("   3. Verify the extension ID in the URL")
        print("   4. Try opening the extension manually first")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
