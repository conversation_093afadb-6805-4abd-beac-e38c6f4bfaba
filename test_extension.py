#!/usr/bin/env python3
"""
Test script to verify Smart Lead Extractor extension accessibility

This script opens Chrome and tries to access the extension to verify it's working.
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options

# Extension URL
EXTENSION_URL = "chrome-extension://dlfcpdjocjjmobbdljgjlmiapgipaobf/views/index.html"
CHROMEDRIVER_PATH = r"C:\Users\<USER>\Desktop\web driver\chromedriver.exe"

def test_extension_access():
    """Test if the extension is accessible"""
    driver = None
    try:
        print("🔄 Setting up Chrome driver...")
        
        # Setup Chrome options
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--window-size=1920,1080")
        
        # Use the specific ChromeDriver path
        if os.path.exists(CHROMEDRIVER_PATH):
            print(f"   Using ChromeDriver from: {CHROMEDRIVER_PATH}")
            service = Service(CHROMEDRIVER_PATH)
            driver = webdriver.Chrome(service=service, options=chrome_options)
        else:
            print(f"   ChromeDriver not found at {CHROMEDRIVER_PATH}")
            return False
        
        print("✅ Chrome driver initialized")
        
        # Try to access the extension
        print(f"🔄 Accessing extension URL: {EXTENSION_URL}")
        driver.get(EXTENSION_URL)
        time.sleep(10)  # Wait for extension to load
        
        # Check current URL
        current_url = driver.current_url
        print(f"   Current URL: {current_url}")
        
        if "chrome-extension://" not in current_url:
            print("❌ Extension not accessible")
            print("   Possible issues:")
            print("   1. Extension not installed")
            print("   2. Extension not enabled")
            print("   3. Wrong extension ID")
            return False
        
        print("✅ Extension URL accessible")
        
        # Check for key interface elements
        print("🔄 Checking interface elements...")
        
        try:
            # Look for keywords textarea
            keywords_textarea = driver.find_element(By.CSS_SELECTOR, "textarea[placeholder='Keywords']")
            print("✅ Keywords textarea found")
        except Exception as e:
            print(f"❌ Keywords textarea not found: {e}")
            return False
        
        try:
            # Look for locations textarea
            locations_textarea = driver.find_element(By.CSS_SELECTOR, "textarea[placeholder='Locations']")
            print("✅ Locations textarea found")
        except Exception as e:
            print(f"❌ Locations textarea not found: {e}")
            return False
        
        try:
            # Look for start button
            start_button = driver.find_element(By.XPATH, "//button[contains(text(), 'Start Extracting')]")
            print("✅ Start button found")
        except Exception as e:
            print(f"❌ Start button not found: {e}")
            return False
        
        try:
            # Look for download button
            download_button = driver.find_element(By.XPATH, "//button[contains(text(), 'Download')]")
            print("✅ Download button found")
        except Exception as e:
            print(f"❌ Download button not found: {e}")
            return False
        
        print("🎉 All interface elements found! Extension is ready for automation.")
        
        # Keep browser open for manual inspection
        print("\n📋 Manual verification:")
        print("   - Browser will stay open for 30 seconds")
        print("   - Please verify the extension interface manually")
        print("   - Check if you can enter text in the textareas")
        
        time.sleep(30)
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False
    
    finally:
        if driver:
            print("🔄 Closing browser...")
            driver.quit()

def main():
    """Main function"""
    print("Smart Lead Extractor - Extension Test")
    print("=" * 50)
    
    if test_extension_access():
        print("\n🎉 Extension test PASSED!")
        print("   You can now run the main automation script.")
    else:
        print("\n❌ Extension test FAILED!")
        print("   Please fix the issues before running automation.")
        print("\n🔧 Troubleshooting steps:")
        print("   1. Install Smart Lead Extractor extension in Chrome")
        print("   2. Enable the extension")
        print("   3. Verify the extension ID in the URL")
        print("   4. Try opening the extension manually first")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
